#!/usr/bin/env python3
"""
Minimal test to isolate the browser automation issue
"""

import asyncio
import sys
import os

# Add the browser automation service to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'browser-automation-service'))

async def test_request_parsing():
    """Test if the BrowserAutomationRequest can be parsed correctly"""
    
    try:
        from app.models.browser_automation import BrowserAutomationRequest, UserTier
        
        print("🧪 Testing BrowserAutomationRequest parsing...")
        
        # Test data similar to what's being sent from the Next.js app
        test_data = {
            "task": "when is <PERSON><PERSON>'s next match?",
            "task_type": "data_extraction",
            "user_id": "69d967d5-0b7b-402b-ae1b-711d9b74eef4",
            "config_id": "b39270c0-feb8-4c99-b6d2-9ee224edd57e",
            "config_name": "openrouter",
            "user_tier": "professional",
            "extracted_parameters": {},
            "api_keys": [
                {
                    "id": "94373577-9efb-4a88-8eba-490625742d16",
                    "provider": "openrouter",
                    "model": "microsoft/phi-4-reasoning:free",
                    "api_key": "test_key_encrypted",
                    "temperature": 1,
                    "label": "llama",
                    "roles": []
                }
            ],
            "browser_headless": True,
            "browser_viewport_width": 1920,
            "browser_viewport_height": 1080,
            "browser_slow_mo": 0,
            "browser_devtools": False,
            "stream": True
        }
        
        # Try to create the request object
        request = BrowserAutomationRequest(**test_data)
        print(f"✅ Request parsed successfully!")
        print(f"📋 User tier: {request.user_tier} (type: {type(request.user_tier)})")
        print(f"📋 User tier value: {request.user_tier.value}")
        
        # Test if we can call json() on it
        try:
            json_data = request.json()
            print(f"✅ JSON serialization successful!")
        except Exception as json_error:
            print(f"❌ JSON serialization failed: {json_error}")
        
        return request
        
    except Exception as e:
        print(f"❌ Request parsing failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_orchestrator_creation():
    """Test if we can create a BrowserOrchestrator"""
    
    request = await test_request_parsing()
    if not request:
        return
    
    try:
        from app.services.browser_orchestrator import BrowserOrchestrator
        from app.services.session_manager import SessionManager
        
        print("\n🧪 Testing BrowserOrchestrator creation...")
        
        # Create a mock session manager
        session_manager = SessionManager()
        
        # Try to create the orchestrator
        orchestrator = BrowserOrchestrator(
            task_id="test_task_123",
            request=request,
            session_manager=session_manager,
            enable_streaming=True,
            test_mode=True
        )
        
        print(f"✅ BrowserOrchestrator created successfully!")
        return orchestrator
        
    except Exception as e:
        print(f"❌ BrowserOrchestrator creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """Main test function"""
    print("🚀 Starting minimal browser automation test...")
    
    # Test request parsing
    await test_request_parsing()
    
    # Test orchestrator creation
    await test_orchestrator_creation()
    
    print("\n✅ Minimal test completed!")

if __name__ == "__main__":
    asyncio.run(main())
