#!/usr/bin/env python3
"""
Debug script to test browser automation service directly
"""

import asyncio
import json
import aiohttp
from typing import Dict, Any

async def test_browser_automation_endpoint():
    """Test the browser automation endpoint directly"""
    
    # Test data similar to what's being sent from the Next.js app
    test_request = {
        "task": "when is <PERSON><PERSON>'s next match?",
        "task_type": "data_extraction",
        "user_id": "69d967d5-0b7b-402b-ae1b-711d9b74eef4",
        "config_id": "b39270c0-feb8-4c99-b6d2-9ee224edd57e",
        "config_name": "openrouter",
        "user_tier": "professional",  # This should be valid as UserTier enum accepts string values
        "extracted_parameters": {},
        "api_keys": [
            {
                "id": "94373577-9efb-4a88-8eba-490625742d16",
                "provider": "openrouter",
                "model": "microsoft/phi-4-reasoning:free",
                "api_key": "test_key_encrypted",
                "temperature": 1,
                "label": "llama",
                "roles": []
            }
        ],
        "browser_headless": True,
        "browser_viewport_width": 1920,
        "browser_viewport_height": 1080,
        "browser_slow_mo": 0,
        "browser_devtools": False,
        "stream": True
    }

    # Also test the non-streaming endpoint to see if the issue is specific to streaming
    test_request_non_stream = test_request.copy()
    test_request_non_stream["stream"] = False
    
    print("🧪 Testing browser automation service...")
    print(f"📋 Request: {json.dumps(test_request, indent=2)}")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test the non-streaming endpoint first
            print("\n🔄 Testing non-streaming endpoint...")
            async with session.post(
                "http://localhost:8000/api/v1/browser/execute",
                json=test_request_non_stream,
                headers={"Content-Type": "application/json"}
            ) as response:
                print(f"📊 Non-streaming response status: {response.status}")
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Non-streaming response: {json.dumps(result, indent=2)}")
                else:
                    error_text = await response.text()
                    print(f"❌ Non-streaming error: {error_text}")

            # Test the streaming endpoint
            print("\n🔄 Testing streaming endpoint...")
            async with session.post(
                "http://localhost:8000/api/v1/browser/execute/stream",
                json=test_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                print(f"📊 Streaming response status: {response.status}")
                print(f"📋 Response headers: {dict(response.headers)}")

                if response.status == 200:
                    print("\n📡 Streaming response:")
                    async for line in response.content:
                        if line:
                            decoded_line = line.decode('utf-8').strip()
                            if decoded_line:
                                print(f"📨 {decoded_line}")
                else:
                    error_text = await response.text()
                    print(f"❌ Streaming error response: {error_text}")

    except Exception as e:
        print(f"❌ Connection error: {e}")
        print("💡 Make sure the browser automation service is running on port 8000")

async def test_health_endpoint():
    """Test the health endpoint"""
    print("\n🏥 Testing health endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/health") as response:
                print(f"📊 Health status: {response.status}")
                if response.status == 200:
                    health_data = await response.json()
                    print(f"✅ Health response: {json.dumps(health_data, indent=2)}")
                else:
                    error_text = await response.text()
                    print(f"❌ Health error: {error_text}")
                    
    except Exception as e:
        print(f"❌ Health check failed: {e}")

async def main():
    """Main test function"""
    print("🚀 Starting browser automation debug test...")
    
    # Test health first
    await test_health_endpoint()
    
    # Test browser automation
    await test_browser_automation_endpoint()
    
    print("\n✅ Debug test completed!")

if __name__ == "__main__":
    asyncio.run(main())
